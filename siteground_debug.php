<?php
/**
 * SiteGround Debug Script
 * This will identify the real issue with edit functionality
 * 
 * IMPORTANT: Delete this file after debugging for security!
 */

session_start();

// Database configuration - UPDATE WITH YOUR SITEGROUND DETAILS
$db_host = 'localhost';
$db_name = 'dbnkkk8lxffmdu';
$db_user = 'uihxynu3jgkt9';
$db_pass = '@:5`|lt+1f1@';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SiteGround Debug - MJ Hauling United LLC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #bee5eb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-danger {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SiteGround Debug Analysis</h1>
        
        <div class="info">
            <strong>Good News:</strong> The user_id column already exists! The issue is something else.
            Let's find the real problem...
        </div>

        <h2>1. Database Structure Analysis</h2>
        <?php
        try {
            $stmt = $pdo->query("DESCRIBE shippment_lead");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo '<div class="success">✅ Database connection successful</div>';
            echo '<div class="success">✅ shippment_lead table exists with ' . count($columns) . ' columns</div>';
            
            echo '<h3>Current Table Structure:</h3>';
            echo '<table>';
            echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
            foreach ($columns as $column) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                echo '<td>' . htmlspecialchars($column['Extra']) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            // Check for required columns
            $existing_columns = array_column($columns, 'Field');
            $required_columns = ['id', 'user_id', 'name', 'email', 'phone'];
            $missing_columns = array_diff($required_columns, $existing_columns);
            
            if (empty($missing_columns)) {
                echo '<div class="success">✅ All required columns exist</div>';
            } else {
                echo '<div class="error">❌ Missing columns: ' . implode(', ', $missing_columns) . '</div>';
            }
            
        } catch (PDOException $e) {
            echo '<div class="error">❌ Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>

        <h2>2. Sample Data Analysis</h2>
        <?php
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM shippment_lead");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $total_leads = $result['total'];
            
            echo '<div class="info">📊 Total leads in database: ' . $total_leads . '</div>';
            
            if ($total_leads > 0) {
                // Get sample data
                $stmt = $pdo->query("SELECT id, name, email, user_id, created_at FROM shippment_lead ORDER BY id DESC LIMIT 5");
                $sample_leads = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo '<h3>Recent Leads Sample:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Email</th><th>User ID</th><th>Created At</th></tr>';
                foreach ($sample_leads as $lead) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($lead['id']) . '</td>';
                    echo '<td>' . htmlspecialchars($lead['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($lead['email']) . '</td>';
                    echo '<td>' . htmlspecialchars($lead['user_id'] ?? 'NULL') . '</td>';
                    echo '<td>' . htmlspecialchars($lead['created_at'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                
                // Check user_id distribution
                $stmt = $pdo->query("SELECT user_id, COUNT(*) as count FROM shippment_lead GROUP BY user_id");
                $user_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo '<h3>User ID Distribution:</h3>';
                echo '<table>';
                echo '<tr><th>User ID</th><th>Number of Leads</th></tr>';
                foreach ($user_distribution as $dist) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($dist['user_id'] ?? 'NULL') . '</td>';
                    echo '<td>' . htmlspecialchars($dist['count']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
        } catch (PDOException $e) {
            echo '<div class="error">❌ Error analyzing data: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>

        <h2>3. Edit Functionality Test</h2>
        <?php
        if (isset($_POST['test_update'])) {
            $test_id = $_POST['test_id'];
            
            try {
                // Test if we can update a lead
                $stmt = $pdo->prepare("UPDATE shippment_lead SET name = CONCAT(name, ' [TESTED]') WHERE id = ?");
                $result = $stmt->execute([$test_id]);
                $affected_rows = $stmt->rowCount();
                
                if ($affected_rows > 0) {
                    echo '<div class="success">✅ UPDATE test successful! Updated ' . $affected_rows . ' row(s)</div>';
                    
                    // Revert the change
                    $stmt = $pdo->prepare("UPDATE shippment_lead SET name = REPLACE(name, ' [TESTED]', '') WHERE id = ?");
                    $stmt->execute([$test_id]);
                    echo '<div class="info">ℹ️ Test change reverted</div>';
                } else {
                    echo '<div class="warning">⚠️ UPDATE test failed - no rows affected</div>';
                }
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ UPDATE test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
        } else {
            // Show test form
            try {
                $stmt = $pdo->query("SELECT id, name FROM shippment_lead ORDER BY id DESC LIMIT 1");
                $test_lead = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($test_lead) {
                    echo '<form method="POST">';
                    echo '<p>Test updating lead ID ' . $test_lead['id'] . ' (' . htmlspecialchars($test_lead['name']) . '):</p>';
                    echo '<input type="hidden" name="test_id" value="' . $test_lead['id'] . '">';
                    echo '<button type="submit" name="test_update" class="btn btn-success">Test Update Functionality</button>';
                    echo '</form>';
                } else {
                    echo '<div class="warning">⚠️ No leads found to test with</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="error">❌ Error preparing test: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
        }
        ?>

        <h2>4. Session Analysis</h2>
        <?php
        echo '<h3>Current Session Data:</h3>';
        if (empty($_SESSION)) {
            echo '<div class="warning">⚠️ No session data found - user not logged in</div>';
            echo '<div class="info">This could be why edit functionality fails. Users need to be logged in.</div>';
        } else {
            echo '<table>';
            echo '<tr><th>Session Key</th><th>Value</th></tr>';
            foreach ($_SESSION as $key => $value) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($key) . '</td>';
                echo '<td>' . htmlspecialchars(is_array($value) ? json_encode($value) : $value) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
        ?>

        <h2>5. File Permissions Check</h2>
        <?php
        $files_to_check = ['edit_lead.php', 'view_leads.php', 'admin.php'];
        
        foreach ($files_to_check as $file) {
            if (file_exists($file)) {
                $perms = fileperms($file);
                $perms_string = substr(sprintf('%o', $perms), -4);
                echo '<div class="success">✅ ' . $file . ' exists (permissions: ' . $perms_string . ')</div>';
            } else {
                echo '<div class="error">❌ ' . $file . ' not found</div>';
            }
        }
        ?>

        <h2>6. Likely Issues and Solutions</h2>
        
        <div class="warning">
            <h3>🎯 Most Likely Causes:</h3>
            <ol>
                <li><strong>Session Issues:</strong> User not properly logged in</li>
                <li><strong>CSRF Token Problems:</strong> Security tokens not matching</li>
                <li><strong>Form Submission Issues:</strong> POST data not reaching the script</li>
                <li><strong>Permission Logic Errors:</strong> User ID mismatches</li>
                <li><strong>File Path Issues:</strong> Incorrect redirects or includes</li>
            </ol>
        </div>

        <div class="info">
            <h3>🔧 Recommended Next Steps:</h3>
            <ol>
                <li><strong>Login Test:</strong> Make sure you're logged in as admin or user</li>
                <li><strong>Try Direct Update:</strong> Use the test button above</li>
                <li><strong>Check Error Logs:</strong> Look at SiteGround error logs</li>
                <li><strong>Test with Simple Lead:</strong> Try editing a lead with minimal data</li>
                <li><strong>Enable Debug Mode:</strong> Add error reporting to edit_lead.php</li>
            </ol>
        </div>

        <div class="code">
// Add this to the top of edit_lead.php for debugging:
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Add this before the UPDATE query:
echo "POST data: ";
print_r($_POST);
echo "Session data: ";
print_r($_SESSION);
        </div>

        <h2>🔗 Quick Actions</h2>
        <a href="admin.php" class="btn">Go to Admin Panel</a>
        <a href="view_leads.php" class="btn">Go to View Leads</a>
        <a href="edit_lead.php?id=1" class="btn">Test Edit Lead</a>
        
        <div class="error">
            <h3>🔒 SECURITY REMINDER:</h3>
            <p><strong>DELETE THIS FILE IMMEDIATELY AFTER DEBUGGING!</strong></p>
            <p>This file contains sensitive database information and should not remain on your server.</p>
        </div>
    </div>
</body>
</html>
