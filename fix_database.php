<?php
/**
 * Database Fix Script
 * This script will add the missing user_id column and fix any database issues
 * 
 * IMPORTANT: Delete this file after running for security reasons!
 */

// Database configuration
$db_host = 'localhost';
$db_name = 'dbnkkk8lxffmdu';
$db_user = 'uihxynu3jgkt9';
$db_pass = '@:5`|lt+1f1@';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Fix Script - MJ Hauling United LLC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Fix Script</h1>
        
        <div class="warning">
            <strong>Security Notice:</strong> This script will fix database issues. Delete this file after running!
        </div>

        <?php
        if (isset($_POST['fix_database'])) {
            echo '<h2>Fixing Database Issues...</h2>';
            
            try {
                // Check if user_id column exists
                $stmt = $pdo->query("DESCRIBE shippment_lead");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $user_id_exists = false;
                foreach ($columns as $column) {
                    if ($column['Field'] === 'user_id') {
                        $user_id_exists = true;
                        break;
                    }
                }
                
                if (!$user_id_exists) {
                    echo '<div class="warning">Adding user_id column to shippment_lead table...</div>';
                    $pdo->exec("ALTER TABLE shippment_lead ADD COLUMN user_id INT NULL");
                    echo '<div class="success">✅ user_id column added successfully!</div>';
                } else {
                    echo '<div class="success">✅ user_id column already exists</div>';
                }
                
                // Check if there are leads without user_id and assign them to admin
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM shippment_lead WHERE user_id IS NULL");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $leads_without_user = $result['count'];
                
                if ($leads_without_user > 0) {
                    echo '<div class="warning">Found ' . $leads_without_user . ' leads without user_id. Assigning them to admin...</div>';
                    
                    // Get first admin user ID
                    $stmt = $pdo->query("SELECT id FROM admin_users LIMIT 1");
                    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($admin) {
                        $admin_id = $admin['id'];
                        $stmt = $pdo->prepare("UPDATE shippment_lead SET user_id = ? WHERE user_id IS NULL");
                        $stmt->execute([$admin_id]);
                        echo '<div class="success">✅ Assigned ' . $leads_without_user . ' leads to admin user (ID: ' . $admin_id . ')</div>';
                    } else {
                        echo '<div class="error">❌ No admin users found. Please create an admin user first.</div>';
                    }
                } else {
                    echo '<div class="success">✅ All leads have user_id assigned</div>';
                }
                
                // Add updated_at column if it doesn't exist
                $updated_at_exists = false;
                foreach ($columns as $column) {
                    if ($column['Field'] === 'updated_at') {
                        $updated_at_exists = true;
                        break;
                    }
                }
                
                if (!$updated_at_exists) {
                    echo '<div class="warning">Adding updated_at column to shippment_lead table...</div>';
                    $pdo->exec("ALTER TABLE shippment_lead ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL");
                    echo '<div class="success">✅ updated_at column added successfully!</div>';
                } else {
                    echo '<div class="success">✅ updated_at column already exists</div>';
                }
                
                echo '<div class="success"><h3>✅ Database Fix Complete!</h3>
                <p>Your database is now properly configured for the edit lead functionality.</p>
                <p><strong>Remember to delete this file for security!</strong></p></div>';
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ Error fixing database: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
        } else {
            // Show current status and fix button
            echo '<h2>Current Database Status</h2>';
            
            try {
                $stmt = $pdo->query("DESCRIBE shippment_lead");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $user_id_exists = false;
                $updated_at_exists = false;
                
                foreach ($columns as $column) {
                    if ($column['Field'] === 'user_id') {
                        $user_id_exists = true;
                    }
                    if ($column['Field'] === 'updated_at') {
                        $updated_at_exists = true;
                    }
                }
                
                if ($user_id_exists) {
                    echo '<div class="success">✅ user_id column exists</div>';
                } else {
                    echo '<div class="error">❌ user_id column is missing</div>';
                }
                
                if ($updated_at_exists) {
                    echo '<div class="success">✅ updated_at column exists</div>';
                } else {
                    echo '<div class="error">❌ updated_at column is missing</div>';
                }
                
                // Check leads without user_id
                if ($user_id_exists) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM shippment_lead WHERE user_id IS NULL");
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $leads_without_user = $result['count'];
                    
                    if ($leads_without_user > 0) {
                        echo '<div class="warning">⚠️ ' . $leads_without_user . ' leads do not have user_id assigned</div>';
                    } else {
                        echo '<div class="success">✅ All leads have user_id assigned</div>';
                    }
                }
                
                if (!$user_id_exists || !$updated_at_exists || (isset($leads_without_user) && $leads_without_user > 0)) {
                    echo '<form method="POST">
                        <button type="submit" name="fix_database" class="btn btn-success">Fix Database Issues</button>
                    </form>';
                } else {
                    echo '<div class="success"><h3>✅ Database is properly configured!</h3></div>';
                }
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ Error checking database: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
        }
        ?>

        <a href="view_leads.php" class="btn">Go to View Leads</a>
        <a href="admin.php" class="btn">Go to Admin Panel</a>
    </div>
</body>
</html>
