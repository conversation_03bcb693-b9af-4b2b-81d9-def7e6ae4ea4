# Email Setup Guide for MJ Hauling United LLC Admin Panel

## Issues Fixed

The admin.php file had 3 major email-related issues that have been resolved:

1. **Duplicate Email Handlers**: There were two different email sending implementations causing conflicts
2. **Incorrect SMTP Configuration**: P<PERSON><PERSON>ailer had placeholder values instead of real SMTP settings
3. **Missing SiteGround Configuration**: No proper configuration for SiteGround hosting

## What Was Changed

### 1. Consolidated Email Functionality
- Removed duplicate email handling code
- Created a single, robust email function using PHPMailer
- Added proper error handling and logging

### 2. Created Email Configuration File
- New file: `email_config.php` contains all SMTP settings
- Separates configuration from code for better security
- Includes settings for multiple email providers

### 3. Updated Admin Panel
- Fixed the email modal form
- Improved validation and error messages
- Added proper debugging options

## Setup Instructions

### Step 1: Update Email Configuration

Edit the `email_config.php` file and replace the placeholder values:

```php
// Update these with your actual email credentials
'smtp_username' => '<EMAIL>', // Your actual email
'smtp_password' => 'your_email_password',  // Your actual email password
'from_email' => '<EMAIL>',    // Your actual email
```

### Step 2: SiteGround Specific Settings

For SiteGround hosting, use these settings in `email_config.php`:

```php
$email_config = [
    'smtp_host' => 'smtp.siteground.com',
    'smtp_port' => 587,
    'smtp_secure' => 'tls',
    'smtp_auth' => true,
    'smtp_username' => '<EMAIL>', // Replace with your domain email
    'smtp_password' => 'your_email_password',       // Replace with your email password
    'from_email' => '<EMAIL>',    // Replace with your domain email
    'from_name' => 'MJ Hauling United LLC',
    'use_html' => true,
    'charset' => 'UTF-8',
    'debug_mode' => false, // Set to true for debugging
];
```

### Step 3: Get Your Email Credentials

1. **Log into your SiteGround cPanel**
2. **Go to Email Accounts section**
3. **Find or create an email account** (e.g., <EMAIL>)
4. **Note down the password** for this email account
5. **Update the email_config.php file** with these credentials

### Step 4: Test Email Functionality

1. **Enable debug mode** temporarily by setting `'debug_mode' => true` in email_config.php
2. **Try sending a test email** from the admin panel
3. **Check the server error logs** if emails fail to send
4. **Disable debug mode** once everything works: `'debug_mode' => false`

## Troubleshooting

### Common Issues and Solutions

#### 1. "SMTP Authentication Failed"
- **Cause**: Wrong username or password
- **Solution**: Double-check email credentials in cPanel

#### 2. "Connection Refused"
- **Cause**: Wrong SMTP host or port
- **Solution**: Verify SMTP settings with SiteGround support

#### 3. "SSL/TLS Connection Failed"
- **Cause**: Wrong encryption type
- **Solution**: Try changing 'tls' to 'ssl' or vice versa

#### 4. Emails Not Received
- **Cause**: Emails might be going to spam
- **Solution**: 
  - Check recipient's spam folder
  - Add SPF/DKIM records to your domain
  - Use a domain email (not Gmail/Yahoo) as sender

### Debug Mode

To enable detailed error logging, set `'debug_mode' => true` in email_config.php. This will:
- Show detailed SMTP connection information
- Log all email sending attempts
- Display specific error messages

**Important**: Always set `'debug_mode' => false` in production!

## Alternative Email Providers

The email_config.php file includes commented examples for:
- Gmail SMTP
- Outlook/Hotmail SMTP
- Generic cPanel hosting SMTP

Uncomment and modify the appropriate section if you're not using SiteGround.

## Security Notes

1. **Never commit email_config.php to version control** with real credentials
2. **Use strong passwords** for email accounts
3. **Consider using app passwords** for Gmail/Outlook
4. **Regularly update email passwords**
5. **Monitor email logs** for suspicious activity

## Support

If you continue to have email issues:

1. **Check SiteGround documentation** for SMTP settings
2. **Contact SiteGround support** for server-specific issues
3. **Review server error logs** for detailed error messages
4. **Test with a simple email client** first to verify SMTP settings

## Files Modified

- `admin.php` - Fixed email functionality and removed duplicates
- `email_config.php` - New configuration file (needs your credentials)
- `EMAIL_SETUP_GUIDE.md` - This setup guide

Remember to backup your files before making any changes!
