<?php
/**
 * SiteGround Database Fix Script
 * This script will fix the database structure on your hosted SiteGround environment
 * 
 * IMPORTANT: 
 * 1. Upload this file to your SiteGround hosting
 * 2. Run it once via browser
 * 3. Delete this file immediately after running for security
 */

// Database configuration - UPDATE THESE WITH YOUR SITEGROUND DATABASE DETAILS
$db_host = 'localhost';
$db_name = 'dbnkkk8lxffmdu'; // Your SiteGround database name
$db_user = 'uihxynu3jgkt9'; // Your SiteGround database user
$db_pass = '@:5`|lt+1f1@'; // Your SiteGround database password

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SiteGround Database Fix - MJ Hauling United LLC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #bee5eb;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            font-family: monospace;
            white-space: pre-wrap;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 SiteGround Database Fix Script</h1>
        
        <div class="warning">
            <strong>⚠️ IMPORTANT SECURITY NOTICE:</strong><br>
            This script will fix your database structure on SiteGround hosting.<br>
            <strong>DELETE THIS FILE IMMEDIATELY AFTER RUNNING!</strong>
        </div>

        <?php
        if (isset($_POST['fix_database'])) {
            echo '<h2>🚀 Fixing SiteGround Database...</h2>';
            
            $fixes_applied = [];
            $errors = [];
            
            try {
                // Get current table structure
                $stmt = $pdo->query("DESCRIBE shippment_lead");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $existing_columns = array_column($columns, 'Field');
                
                echo '<div class="info">📋 Current table columns: ' . implode(', ', $existing_columns) . '</div>';
                
                // Fix 1: Add user_id column if missing
                if (!in_array('user_id', $existing_columns)) {
                    try {
                        $pdo->exec("ALTER TABLE shippment_lead ADD COLUMN user_id INT NULL AFTER id");
                        $fixes_applied[] = "✅ Added user_id column";
                    } catch (PDOException $e) {
                        $errors[] = "❌ Failed to add user_id column: " . $e->getMessage();
                    }
                } else {
                    $fixes_applied[] = "✅ user_id column already exists";
                }
                
                // Fix 2: Add updated_at column if missing
                if (!in_array('updated_at', $existing_columns)) {
                    try {
                        $pdo->exec("ALTER TABLE shippment_lead ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL");
                        $fixes_applied[] = "✅ Added updated_at column";
                    } catch (PDOException $e) {
                        $errors[] = "❌ Failed to add updated_at column: " . $e->getMessage();
                    }
                } else {
                    $fixes_applied[] = "✅ updated_at column already exists";
                }
                
                // Fix 3: Ensure created_at column exists and has proper default
                if (!in_array('created_at', $existing_columns)) {
                    try {
                        $pdo->exec("ALTER TABLE shippment_lead ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                        $fixes_applied[] = "✅ Added created_at column";
                    } catch (PDOException $e) {
                        $errors[] = "❌ Failed to add created_at column: " . $e->getMessage();
                    }
                } else {
                    $fixes_applied[] = "✅ created_at column already exists";
                }
                
                // Fix 4: Assign user_id to existing leads without one
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM shippment_lead WHERE user_id IS NULL");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $leads_without_user = $result['count'];
                
                if ($leads_without_user > 0) {
                    // Get first admin user ID, or create a default user_id of 1
                    try {
                        $stmt = $pdo->query("SELECT id FROM admin_users ORDER BY id ASC LIMIT 1");
                        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
                        $default_user_id = $admin ? $admin['id'] : 1;
                        
                        $stmt = $pdo->prepare("UPDATE shippment_lead SET user_id = ? WHERE user_id IS NULL");
                        $stmt->execute([$default_user_id]);
                        $fixes_applied[] = "✅ Assigned user_id to {$leads_without_user} existing leads (user_id: {$default_user_id})";
                    } catch (PDOException $e) {
                        $errors[] = "❌ Failed to assign user_id to existing leads: " . $e->getMessage();
                    }
                } else {
                    $fixes_applied[] = "✅ All leads already have user_id assigned";
                }
                
                // Fix 5: Update table structure for better performance
                try {
                    // Add index on user_id for better performance
                    $pdo->exec("ALTER TABLE shippment_lead ADD INDEX idx_user_id (user_id)");
                    $fixes_applied[] = "✅ Added index on user_id column";
                } catch (PDOException $e) {
                    // Index might already exist, that's okay
                    if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                        $errors[] = "⚠️ Could not add index: " . $e->getMessage();
                    } else {
                        $fixes_applied[] = "✅ Index on user_id already exists";
                    }
                }
                
                // Display results
                if (!empty($fixes_applied)) {
                    echo '<div class="success"><h3>🎉 Fixes Applied Successfully:</h3><ul>';
                    foreach ($fixes_applied as $fix) {
                        echo '<li>' . $fix . '</li>';
                    }
                    echo '</ul></div>';
                }
                
                if (!empty($errors)) {
                    echo '<div class="error"><h3>❌ Errors Encountered:</h3><ul>';
                    foreach ($errors as $error) {
                        echo '<li>' . $error . '</li>';
                    }
                    echo '</ul></div>';
                }
                
                // Final verification
                echo '<h3>🔍 Final Database Structure Verification:</h3>';
                $stmt = $pdo->query("DESCRIBE shippment_lead");
                $final_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo '<table>';
                echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
                foreach ($final_columns as $column) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                
                echo '<div class="success">
                    <h3>🎯 Database Fix Complete!</h3>
                    <p><strong>Your SiteGround database is now properly configured for edit functionality.</strong></p>
                    <p>✅ Users can now edit their own leads</p>
                    <p>✅ Admins can edit all leads</p>
                    <p>✅ Database updates will work correctly</p>
                    <p>✅ Permission system is properly implemented</p>
                </div>';
                
                echo '<div class="warning">
                    <h3>🔒 CRITICAL SECURITY STEP:</h3>
                    <p><strong>DELETE THIS FILE NOW!</strong></p>
                    <p>This script contains database credentials and should not remain on your server.</p>
                </div>';
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ Critical database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            
        } else {
            // Show current status and fix options
            echo '<h2>📊 Current SiteGround Database Status</h2>';
            
            try {
                // Check database connection
                echo '<div class="success">✅ Database connection successful</div>';
                
                // Check table structure
                $stmt = $pdo->query("DESCRIBE shippment_lead");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $existing_columns = array_column($columns, 'Field');
                
                echo '<div class="info">📋 Current columns: ' . implode(', ', $existing_columns) . '</div>';
                
                // Check for required columns
                $required_columns = ['user_id', 'updated_at', 'created_at'];
                $missing_columns = [];
                
                foreach ($required_columns as $col) {
                    if (!in_array($col, $existing_columns)) {
                        $missing_columns[] = $col;
                    }
                }
                
                if (!empty($missing_columns)) {
                    echo '<div class="error">❌ Missing required columns: ' . implode(', ', $missing_columns) . '</div>';
                } else {
                    echo '<div class="success">✅ All required columns exist</div>';
                }
                
                // Check for leads without user_id
                if (in_array('user_id', $existing_columns)) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM shippment_lead WHERE user_id IS NULL");
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $leads_without_user = $result['count'];
                    
                    if ($leads_without_user > 0) {
                        echo '<div class="warning">⚠️ ' . $leads_without_user . ' leads do not have user_id assigned</div>';
                    } else {
                        echo '<div class="success">✅ All leads have user_id assigned</div>';
                    }
                }
                
                // Check total leads
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM shippment_lead");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo '<div class="info">📈 Total leads in database: ' . $result['total'] . '</div>';
                
                // Show fix button if needed
                if (!empty($missing_columns) || (isset($leads_without_user) && $leads_without_user > 0)) {
                    echo '<form method="POST" style="margin: 30px 0;">
                        <button type="submit" name="fix_database" class="btn btn-success">
                            🔧 Fix SiteGround Database Issues
                        </button>
                    </form>';
                } else {
                    echo '<div class="success">
                        <h3>✅ Your database is properly configured!</h3>
                        <p>The edit functionality should work correctly now.</p>
                    </div>';
                }
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                echo '<div class="warning">
                    <h3>🔧 Manual Fix Required:</h3>
                    <p>If the automatic fix fails, run these SQL commands manually in your SiteGround cPanel phpMyAdmin:</p>
                    <div class="code">ALTER TABLE shippment_lead ADD COLUMN user_id INT NULL AFTER id;
ALTER TABLE shippment_lead ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
ALTER TABLE shippment_lead ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
UPDATE shippment_lead SET user_id = 1 WHERE user_id IS NULL;</div>
                </div>';
            }
        }
        ?>

        <h2>🔗 Quick Links</h2>
        <a href="view_leads.php" class="btn">Test View Leads</a>
        <a href="edit_lead.php?id=1" class="btn">Test Edit Lead</a>
        <a href="admin.php" class="btn">Admin Panel</a>
        
        <div class="info">
            <h3>📝 After Running This Fix:</h3>
            <ol>
                <li><strong>Test the edit functionality</strong> - Try editing a lead</li>
                <li><strong>Verify data saves correctly</strong> - Check that changes persist</li>
                <li><strong>Test user permissions</strong> - Ensure users see only their leads</li>
                <li><strong>DELETE THIS FILE</strong> - Remove it from your server immediately</li>
            </ol>
        </div>
    </div>
</body>
</html>
