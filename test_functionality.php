<?php
/**
 * Functionality Test Script
 * Tests all the fixed functionality
 * 
 * IMPORTANT: Delete this file after testing for security reasons!
 */

session_start();

// Database configuration
$db_host = 'localhost';
$db_name = 'dbnkkk8lxffmdu';
$db_user = 'uihxynu3jgkt9';
$db_pass = '@:5`|lt+1f1@';

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Functionality Test - MJ <PERSON>uling United LLC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Functionality Test Results</h1>
        
        <div class="warning">
            <strong>Security Notice:</strong> This is a test script. Delete this file after testing!
        </div>

        <div class="test-section">
            <h2>1. Database Structure Test</h2>
            <?php
            try {
                $stmt = $pdo->query("DESCRIBE shippment_lead");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $required_columns = ['id', 'user_id', 'name', 'email', 'phone', 'updated_at'];
                $missing_columns = [];
                $existing_columns = array_column($columns, 'Field');
                
                foreach ($required_columns as $col) {
                    if (!in_array($col, $existing_columns)) {
                        $missing_columns[] = $col;
                    }
                }
                
                if (empty($missing_columns)) {
                    echo '<div class="success">✅ All required columns exist in shippment_lead table</div>';
                } else {
                    echo '<div class="error">❌ Missing columns: ' . implode(', ', $missing_columns) . '</div>';
                }
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>2. Email Configuration Test</h2>
            <?php
            if (file_exists('email_config.php')) {
                $email_config = require 'email_config.php';
                echo '<div class="success">✅ email_config.php file exists</div>';
                
                $required_config = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email'];
                $missing_config = [];
                
                foreach ($required_config as $config) {
                    if (!isset($email_config[$config]) || empty($email_config[$config])) {
                        $missing_config[] = $config;
                    }
                }
                
                if (empty($missing_config)) {
                    echo '<div class="success">✅ All required email configuration exists</div>';
                } else {
                    echo '<div class="warning">⚠️ Missing or empty email config: ' . implode(', ', $missing_config) . '</div>';
                }
            } else {
                echo '<div class="error">❌ email_config.php file not found</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>3. PHPMailer Files Test</h2>
            <?php
            $phpmailer_files = [
                'PHPMailer/src/PHPMailer.php',
                'PHPMailer/src/SMTP.php',
                'PHPMailer/src/Exception.php'
            ];
            
            $missing_files = [];
            foreach ($phpmailer_files as $file) {
                if (!file_exists($file)) {
                    $missing_files[] = $file;
                }
            }
            
            if (empty($missing_files)) {
                echo '<div class="success">✅ All PHPMailer files exist</div>';
            } else {
                echo '<div class="error">❌ Missing PHPMailer files: ' . implode(', ', $missing_files) . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>4. Sample Data Test</h2>
            <?php
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM shippment_lead");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $total_leads = $result['total'];
                
                echo '<div class="success">✅ Total leads in database: ' . $total_leads . '</div>';
                
                if ($total_leads > 0) {
                    $stmt = $pdo->query("SELECT id, name, email, user_id FROM shippment_lead LIMIT 5");
                    $sample_leads = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo '<h3>Sample Leads:</h3>';
                    echo '<table>';
                    echo '<tr><th>ID</th><th>Name</th><th>Email</th><th>User ID</th></tr>';
                    foreach ($sample_leads as $lead) {
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($lead['id']) . '</td>';
                        echo '<td>' . htmlspecialchars($lead['name']) . '</td>';
                        echo '<td>' . htmlspecialchars($lead['email']) . '</td>';
                        echo '<td>' . htmlspecialchars($lead['user_id'] ?? 'NULL') . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                }
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ Error fetching sample data: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>5. Session Test</h2>
            <?php
            if (isset($_SESSION['admin_logged_in']) || isset($_SESSION['user_logged_in'])) {
                echo '<div class="success">✅ User session is active</div>';
                
                if (isset($_SESSION['admin_logged_in'])) {
                    echo '<p><strong>Admin Session:</strong></p>';
                    echo '<ul>';
                    echo '<li>Admin ID: ' . ($_SESSION['admin_id'] ?? 'Not set') . '</li>';
                    echo '<li>Admin Name: ' . ($_SESSION['admin_name'] ?? 'Not set') . '</li>';
                    echo '<li>Admin Email: ' . ($_SESSION['admin_email'] ?? 'Not set') . '</li>';
                    echo '</ul>';
                }
                
                if (isset($_SESSION['user_logged_in'])) {
                    echo '<p><strong>User Session:</strong></p>';
                    echo '<ul>';
                    echo '<li>User ID: ' . ($_SESSION['user_id'] ?? 'Not set') . '</li>';
                    echo '<li>User Name: ' . ($_SESSION['user_name'] ?? 'Not set') . '</li>';
                    echo '<li>User Email: ' . ($_SESSION['user_email'] ?? 'Not set') . '</li>';
                    echo '</ul>';
                }
            } else {
                echo '<div class="warning">⚠️ No active user session. Please login to test full functionality.</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h2>6. File Permissions Test</h2>
            <?php
            $files_to_check = ['view_leads.php', 'edit_lead.php', 'admin.php'];
            
            foreach ($files_to_check as $file) {
                if (file_exists($file)) {
                    if (is_readable($file)) {
                        echo '<div class="success">✅ ' . $file . ' is readable</div>';
                    } else {
                        echo '<div class="error">❌ ' . $file . ' is not readable</div>';
                    }
                } else {
                    echo '<div class="error">❌ ' . $file . ' does not exist</div>';
                }
            }
            ?>
        </div>

        <h2>Test Summary</h2>
        <div class="success">
            <h3>✅ What Should Work Now:</h3>
            <ul>
                <li><strong>Edit Lead Functionality:</strong> Users can edit their own leads, admins can edit all leads</li>
                <li><strong>Email Modal:</strong> Click email buttons to open modal with auto-selected sender email</li>
                <li><strong>Permission System:</strong> Proper user/admin separation</li>
                <li><strong>Database Updates:</strong> Lead updates should save correctly</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ Next Steps:</h3>
            <ol>
                <li>Run the database fix script if any issues were found</li>
                <li>Configure email settings in email_config.php</li>
                <li>Test the email functionality with real email addresses</li>
                <li>Delete all test files (check_database.php, fix_database.php, test_functionality.php)</li>
            </ol>
        </div>

        <a href="view_leads.php" class="btn">Test View Leads</a>
        <a href="admin.php" class="btn">Test Admin Panel</a>
        <a href="fix_database.php" class="btn">Fix Database</a>
    </div>
</body>
</html>
