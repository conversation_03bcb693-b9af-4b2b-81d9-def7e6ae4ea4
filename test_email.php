<?php
/**
 * Email Test Script for MJ Hauling United LLC
 *
 * This script helps test the native PHP mail functionality.
 * Run this script to verify your email settings are working correctly.
 *
 * IMPORTANT: Delete this file after testing for security reasons!
 */

/**
 * Function to send email using native PHP mail() function
 */
function sendTestEmail($to_email, $subject, $message_body, $from_email = null, $from_name = null) {
    // Set default values if not provided
    $from_email = $from_email ?: '<EMAIL>';
    $from_name = $from_name ?: 'MJ Hauling United LLC';

    // Prepare headers
    $headers = [];
    $headers[] = "MIME-Version: 1.0";
    $headers[] = "Content-type: text/html; charset=UTF-8";
    $headers[] = "From: {$from_name} <{$from_email}>";
    $headers[] = "Reply-To: {$from_email}";
    $headers[] = "X-Mailer: PHP/" . phpversion();

    // Convert message to HTML format
    $html_message = nl2br(htmlspecialchars($message_body, ENT_QUOTES, 'UTF-8'));

    // Send email using native mail function
    $result = mail($to_email, $subject, $html_message, implode("\r\n", $headers));

    return [
        'success' => $result,
        'headers' => $headers,
        'html_message' => $html_message
    ];
}

// Test email settings
$test_email = '<EMAIL>'; // Change this to your test email address
$test_subject = '[MJ Hauling] Email Test from Native PHP Mail';
$test_message = 'This is a test email to verify that the native PHP mail() function is working correctly with the new email system.';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration Test - MJ Hauling United LLC</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #bee5eb;
        }
        .config-display {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            font-family: monospace;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Native PHP Mail Test</h1>

        <div class="info">
            <strong>Important:</strong> This is a test script to verify your native PHP mail configuration.
            Delete this file after testing for security reasons!
        </div>

        <h2>Current Configuration</h2>
        <div class="config-display">
            <strong>Mail Function:</strong> Native PHP mail()<br>
            <strong>Default From Email:</strong> <EMAIL><br>
            <strong>Default From Name:</strong> MJ Hauling United LLC<br>
            <strong>Content Type:</strong> HTML with UTF-8 charset<br>
            <strong>PHP Version:</strong> <?php echo phpversion(); ?><br>
            <strong>Mail Function Available:</strong> <?php echo function_exists('mail') ? 'Yes' : 'No'; ?>
        </div>

        <?php
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
            $recipient = filter_var($_POST['recipient'], FILTER_SANITIZE_EMAIL);

            if (!filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
                echo '<div class="error">Please enter a valid email address.</div>';
            } else {
                // Test email sending using native mail function
                $result = sendTestEmail($recipient, $test_subject, $test_message);

                if ($result['success']) {
                    echo '<div class="success">✅ Test email sent successfully to ' . htmlspecialchars($recipient) . '!</div>';

                    // Show email details
                    echo '<div class="info">';
                    echo '<strong>Email Details:</strong><br>';
                    echo '<strong>Headers:</strong><br>';
                    echo '<pre>' . htmlspecialchars(implode("\r\n", $result['headers'])) . '</pre>';
                    echo '<strong>Message Body:</strong><br>';
                    echo '<div style="border: 1px solid #ccc; padding: 10px; background: #f9f9f9; margin: 10px 0;">';
                    echo $result['html_message'];
                    echo '</div>';
                    echo '</div>';

                } else {
                    echo '<div class="error">❌ Email could not be sent using native PHP mail() function.</div>';

                    // Additional troubleshooting info
                    echo '<div class="info">';
                    echo '<strong>Troubleshooting Tips:</strong><br>';
                    echo '• Check that your server supports the mail() function<br>';
                    echo '• Verify that your hosting provider allows email sending<br>';
                    echo '• Make sure the recipient email address is valid<br>';
                    echo '• Check your server\'s mail configuration<br>';
                    echo '• Contact your hosting provider if the issue persists<br>';
                    echo '• Some shared hosting providers require additional configuration';
                    echo '</div>';
                }
            }
        }
        ?>

        <h2>Send Test Email</h2>
        <form method="POST">
            <div class="form-group">
                <label for="recipient">Test Email Address:</label>
                <input type="email" id="recipient" name="recipient" value="<?php echo htmlspecialchars($test_email); ?>" required>
                <small>Enter your email address to receive a test email</small>
            </div>
            <button type="submit" name="test_email" class="btn">Send Test Email</button>
        </form>

        <h2>New Features Added</h2>
        <div class="success">
            ✅ <strong>Email system has been successfully updated!</strong><br><br>
            <strong>View Leads Page Features:</strong><br>
            • Native PHP mail() function (no PHPMailer dependency)<br>
            • Bulk email functionality with multiple lead selection<br>
            • Checkbox selection for individual leads<br>
            • "Select All" functionality<br>
            • Bulk email modal with personalization options<br>
            • Email sent from logged-in user's email address<br>
            • Customer name personalization with [CUSTOMER_NAME] placeholder<br>
            • Responsive design for mobile devices<br><br>

            <strong>Shipment Lead Page Features:</strong><br>
            • ✅ Auto-email to customer when "Save to Database" is clicked<br>
            • ✅ Email sent from logged-in user's email address<br>
            • ✅ Uses the "Formatted Message" content as email body<br>
            • ✅ Automatic email subject with customer name and quote ID<br>
            • ✅ Email validation before sending<br>
            • ✅ Success/error feedback for both database save and email sending
        </div>

        <h2>How to Use the New Features</h2>
        <div class="info">
            <strong>View Leads Page - Single Email:</strong><br>
            1. Go to view_leads.php<br>
            2. Click the "Email" button next to any lead<br>
            3. Fill in the subject and message<br>
            4. Choose your email address from the dropdown<br>
            5. Send the email<br><br>

            <strong>View Leads Page - Bulk Email:</strong><br>
            1. Go to view_leads.php<br>
            2. Select leads using checkboxes or "Select All"<br>
            3. Click "Send Bulk Email" button at the top<br>
            4. Use [CUSTOMER_NAME] in subject/message for personalization<br>
            5. Choose your email address and send<br><br>

            <strong>Shipment Lead Page - Auto Email:</strong><br>
            1. Go to shippment_lead.php<br>
            2. Fill in the lead information and format the message<br>
            3. Click "Save to Database" button<br>
            4. ✅ Data saves to database AND email automatically sends to customer<br>
            5. ✅ Email uses the "Formatted Message" content<br>
            6. ✅ Email is sent from your logged-in user email address<br>
            7. ✅ Success message shows both database save and email status
        </div>

        <h2>Next Steps</h2>
        <div class="info">
            <strong>If the test email works:</strong><br>
            1. Delete this test_email.php file for security<br>
            2. Your email functionality is ready to use<br>
            3. Go to view_leads.php to start using the new features<br><br>

            <strong>If the test email fails:</strong><br>
            1. Check the error message above<br>
            2. Contact your hosting provider about mail() function support<br>
            3. Verify server mail configuration<br>
            4. Some shared hosts may require additional setup
        </div>

        <a href="shippment_lead.php" class="btn">Go to Shipment Lead Page</a>
        <a href="view_leads.php" class="btn">Go to View Leads</a>
        <a href="admin.php" class="btn" style="background-color: #28a745;">Go to Admin Panel</a>
    </div>
</body>
</html>
