# Edit Lead Permission Fix Guide

## Issues Identified and Fixed

### **Problem 1: Missing Permission Checks**
The original `edit_lead.php` file allowed any logged-in user (admin or regular user) to edit any lead by simply providing the lead ID in the URL. This was a security vulnerability.

### **Problem 2: "Invalid lead ID provided for editing" Error**
This error occurred when users tried to edit leads they didn't have permission to access, but the error message was misleading.

### **Problem 3: Data Not Updating**
When regular users tried to edit leads that belonged to other users, the UPDATE query would fail silently because it couldn't find a matching record with both the correct ID and user_id.

## Solutions Implemented

### **1. Added Authentication Checks**
```php
// Check authentication and get user information
$is_admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$is_user_logged_in = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;

$logged_in_admin_id = $_SESSION['admin_id'] ?? null;
$logged_in_user_id = $_SESSION['user_id'] ?? null;

// Enforce login requirement
if (!$is_admin_logged_in && !$is_user_logged_in) {
    redirectWithStatus('user_login.php', 'error', 'Please log in to access this page.');
}
```

### **2. Added Permission Checks for Lead Fetching**
```php
// Build query with permission check
$sql = "SELECT * FROM shippment_lead WHERE id = :id";
$params = [':id' => $current_lead_id];

// If regular user is logged in, ensure they can only edit their own leads
if ($is_user_logged_in && $logged_in_user_id !== null) {
    $sql .= " AND user_id = :user_id";
    $params[':user_id'] = $logged_in_user_id;
}
// Admin can edit any lead, so no additional restriction needed
```

### **3. Added Permission Checks for Updates**
```php
// Add user permission check to UPDATE query for regular users
if ($is_user_logged_in && $logged_in_user_id !== null) {
    $sql .= " AND user_id = :user_id";
    $update_params[':user_id'] = $logged_in_user_id;
}
```

### **4. Improved Error Messages**
- Clear distinction between "lead not found" and "permission denied"
- Better feedback when updates fail due to permission issues

## How It Works Now

### **For Regular Users:**
1. Can only see the edit form for leads they created (`user_id` matches their session `user_id`)
2. Can only update leads they own
3. Get clear error messages if they try to access leads they don't own
4. UPDATE queries include `user_id` check to prevent unauthorized modifications

### **For Admin Users:**
1. Can edit any lead in the system
2. No `user_id` restrictions applied
3. Full access to all leads

### **Security Features:**
1. **Double Permission Check**: Both when loading the form AND when submitting updates
2. **SQL-Level Protection**: Permission checks are enforced at the database query level
3. **Session Validation**: Proper authentication checks before any operations
4. **CSRF Protection**: Existing CSRF token system remains intact

## Database Requirements

The system requires a `user_id` column in the `shippment_lead` table:

```sql
ALTER TABLE shippment_lead ADD COLUMN user_id INT NULL;
```

### **Data Integrity:**
- New leads automatically get the correct `user_id` when created
- Existing leads without `user_id` are only visible to admins
- You can optionally assign existing leads to users:
  ```sql
  UPDATE shippment_lead SET user_id = 1 WHERE user_id IS NULL;
  ```

## Testing the Fix

### **Test Cases:**

1. **Regular User Login:**
   - Should only see their own leads in `view_leads.php`
   - Should only be able to edit their own leads
   - Should get permission error when trying to edit others' leads

2. **Admin Login:**
   - Should see all leads in `view_leads.php`
   - Should be able to edit any lead
   - No permission restrictions

3. **URL Manipulation Test:**
   - Regular user tries to access `edit_lead.php?id=X` where X is not their lead
   - Should get "permission denied" error, not "invalid ID"

4. **Form Submission Test:**
   - Regular user tries to submit edit form for lead they don't own
   - Should get permission error and no data should be updated

## Files Modified

1. **`edit_lead.php`** - Added comprehensive permission checking
2. **`check_database.php`** - New diagnostic script (delete after use)
3. **`EDIT_LEAD_FIX_GUIDE.md`** - This documentation

## Verification Steps

1. **Run the database check:**
   - Visit `check_database.php` in your browser
   - Verify `user_id` column exists
   - Check that leads have proper `user_id` values
   - Delete the check script after verification

2. **Test user permissions:**
   - Login as a regular user
   - Try to edit a lead (should work for own leads)
   - Try to access another user's lead via URL manipulation (should fail)

3. **Test admin permissions:**
   - Login as admin
   - Should be able to edit any lead
   - No permission restrictions

## Troubleshooting

### **If you get "user_id column doesn't exist" error:**
```sql
ALTER TABLE shippment_lead ADD COLUMN user_id INT NULL;
```

### **If existing leads aren't visible to users:**
```sql
UPDATE shippment_lead SET user_id = 1 WHERE user_id IS NULL;
```
(Replace `1` with appropriate user ID)

### **If permission errors persist:**
1. Check that `$_SESSION['user_id']` is properly set during login
2. Verify the user exists in the `local_users` table
3. Check that the lead's `user_id` matches the session `user_id`

## Security Notes

- ✅ **SQL Injection Protected**: All queries use prepared statements
- ✅ **Permission Enforced**: Database-level permission checks
- ✅ **Session Validated**: Proper authentication required
- ✅ **CSRF Protected**: Existing token system maintained
- ✅ **Error Handling**: Informative but secure error messages

The system now properly separates user and admin permissions while maintaining data security and integrity.
